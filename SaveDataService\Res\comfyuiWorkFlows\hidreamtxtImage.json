{"id": "a3aa8028-bdba-4cc4-9ecc-f8256ff6b996", "revision": 0, "last_node_id": 88, "last_link_id": 196, "nodes": [{"id": 73, "type": "Note", "pos": [221.36090087890625, -222.29476928710938], "size": [250, 88], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["You can try changing the weight_dtype to fp8 if you are running out of memory."], "color": "#432", "bgcolor": "#653"}, {"id": 54, "type": "QuadrupleCLIPLoader", "pos": [80, 50], "size": [400, 130], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "clip_name1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "clip_name2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "clip_name3", "name": "clip_name3", "type": "COMBO", "widget": {"name": "clip_name3"}, "link": null}, {"localized_name": "clip_name4", "name": "clip_name4", "type": "COMBO", "widget": {"name": "clip_name4"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [111, 112]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "QuadrupleCLIPLoader"}, "widgets_values": ["clip_l_hidream.safetensors", "clip_g_hidream.safetensors", "t5xxl_fp8_e4m3fn.safetensors", "llama_3.1_8b_instruct_fp8_scaled.safetensors"], "color": "#223", "bgcolor": "#335"}, {"id": 70, "type": "ModelSamplingSD3", "pos": [543, -351], "size": [210, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 162}, {"localized_name": "移位", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [183]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "ModelSamplingSD3"}, "widgets_values": [3.0000000000000004]}, {"id": 16, "type": "CLIPTextEncode", "pos": [509, -5], "size": [432, 192], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 111}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 189}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [185]}], "title": "Positive Prompt", "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["anime style, A woman sitting at a desk, wearing a black long-sleeved top, and wearing round glasses.\n"], "color": "#232", "bgcolor": "#353"}, {"id": 75, "type": "Note", "pos": [991.0171508789062, 429.44830322265625], "size": [415.6177062988281, 395.4561462402344], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["## Official sampling settings\n\nProvided for reference, my workflows may have slightly different settings.\n\n### HiDream Full\n\n* hidream_i1_full_fp16.safetensors\n* shift: 3.0\n* steps: 50\n* sampler: uni_pc\n* scheduler: simple\n* cfg: 5.0\n\n### HiDream Dev\n\n* hidream_i1_dev_bf16.safetensors\n* shift: 6.0\n* steps: 28\n* sampler: lcm\n* scheduler: normal\n* cfg: 1.0 (no negative prompt)\n\n### HiDream Fast\n\n* hidream_i1_fast_bf16.safetensors\n* shift: 3.0\n* steps: 16\n* sampler: lcm\n* scheduler: normal\n* cfg: 1.0 (no negative prompt)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 40, "type": "CLIPTextEncode", "pos": [521, 256], "size": [432, 192], "flags": {"collapsed": false}, "order": 13, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 112}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 190}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [186]}], "title": "Negative Prompt", "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["blurry"], "color": "#322", "bgcolor": "#533"}, {"id": 69, "type": "UNETLoader", "pos": [80, -90], "size": [400, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [162]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "UNETLoader"}, "widgets_values": ["hidream_full_il_model.safetensors", "default"], "color": "#223", "bgcolor": "#335"}, {"id": 8, "type": "VAEDecode", "pos": [1059.3848876953125, -68.06839752197266], "size": [210, 46], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 188}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 107}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [51]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 77, "type": "VAEEncode", "pos": [758.2686767578125, 499.2914123535156], "size": [210, 46], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 196}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 165}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [187]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 55, "type": "VAELoader", "pos": [141.3599853515625, 248.306640625], "size": [310, 60], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [107, 165]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 88, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [165.27194213867188, 377.3018798828125], "size": [274.9136657714844, 330], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "shape": 7, "type": "IMAGE", "link": 195}, {"localized_name": "遮罩", "name": "mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "宽高比", "name": "aspect_ratio", "type": "COMBO", "widget": {"name": "aspect_ratio"}, "link": null}, {"localized_name": "比例宽度", "name": "proportional_width", "type": "INT", "widget": {"name": "proportional_width"}, "link": null}, {"localized_name": "比例高度", "name": "proportional_height", "type": "INT", "widget": {"name": "proportional_height"}, "link": null}, {"localized_name": "适应", "name": "fit", "type": "COMBO", "widget": {"name": "fit"}, "link": null}, {"localized_name": "方法", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "四舍五入到倍数", "name": "round_to_multiple", "type": "COMBO", "widget": {"name": "round_to_multiple"}, "link": null}, {"localized_name": "缩放至边", "name": "scale_to_side", "type": "COMBO", "widget": {"name": "scale_to_side"}, "link": null}, {"localized_name": "缩放至长度", "name": "scale_to_length", "type": "INT", "widget": {"name": "scale_to_length"}, "link": null}, {"localized_name": "背景颜色", "name": "background_color", "type": "STRING", "widget": {"name": "background_color"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [196]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": null}, {"localized_name": "original_size", "name": "original_size", "type": "BOX", "links": null}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "c0fb64d0ebcb81c6c445a8af79ecee24bc3845b0", "Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2"}, "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "8", "longest", 1344, "#000000"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 84, "type": "SDXLPromptStyler", "pos": [-316.5544128417969, -194.49908447265625], "size": [400, 258], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "text_positive", "name": "text_positive", "type": "STRING", "widget": {"name": "text_positive"}, "link": 193}, {"localized_name": "text_negative", "name": "text_negative", "type": "STRING", "widget": {"name": "text_negative"}, "link": null}, {"localized_name": "style", "name": "style", "type": "COMBO", "widget": {"name": "style"}, "link": null}, {"localized_name": "log_prompt", "name": "log_prompt", "type": "BOOLEAN", "widget": {"name": "log_prompt"}, "link": null}, {"localized_name": "style_positive", "name": "style_positive", "type": "BOOLEAN", "widget": {"name": "style_positive"}, "link": null}, {"localized_name": "style_negative", "name": "style_negative", "type": "BOOLEAN", "widget": {"name": "style_negative"}, "link": null}], "outputs": [{"localized_name": "text_positive", "name": "text_positive", "type": "STRING", "links": [189]}, {"localized_name": "text_negative", "name": "text_negative", "type": "STRING", "links": [190]}], "properties": {"cnr_id": "sdxl_prompt_styler", "ver": "51068179927f79dce14f38c6b1984390ab242be2", "Node name for S&R": "SDXLPromptStyler"}, "widgets_values": ["", "blurry", "sai-3d-model", "No", "", true]}, {"id": 87, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-1097.5743408203125, -162.42335510253906], "size": [270, 78], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}], "outputs": [{"localized_name": "model", "name": "model", "type": "JANUS_MODEL", "links": [192]}, {"localized_name": "processor", "name": "processor", "type": "JANUS_PROCESSOR", "links": [194]}], "properties": {"cnr_id": "janus-pro", "ver": "4400129e5c33664ae6e927162a39ba4116f44b8b", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["deepseek-ai/Janus-Pro-7B"]}, {"id": 82, "type": "K<PERSON><PERSON><PERSON>", "pos": [1022, 84], "size": [315, 262], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 183}, {"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 185}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 186}, {"localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 187}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [188]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [1007367675617122, "randomize", 35, 5, "uni_pc", "simple", 0.7000000000000001]}, {"id": 76, "type": "LoadImage", "pos": [1422.464599609375, -159.37904357910156], "size": [607.9408569335938, 1027.362548828125], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [191, 195]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_00248_.png", "image"]}, {"id": 9, "type": "SaveImage", "pos": [2040.26171875, -137.93994140625], "size": [559.9427490234375, 1001.8128662109375], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 51}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"]}, {"id": 86, "type": "JanusImageUnderstanding", "pos": [-764.0342407226562, -189.9761199951172], "size": [400, 248], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "JANUS_MODEL", "link": 192}, {"localized_name": "processor", "name": "processor", "type": "JANUS_PROCESSOR", "link": 194}, {"localized_name": "image", "name": "image", "type": "IMAGE", "link": 191}, {"localized_name": "question", "name": "question", "type": "STRING", "widget": {"name": "question"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "temperature", "name": "temperature", "type": "FLOAT", "widget": {"name": "temperature"}, "link": null}, {"localized_name": "top_p", "name": "top_p", "type": "FLOAT", "widget": {"name": "top_p"}, "link": null}, {"localized_name": "max_new_tokens", "name": "max_new_tokens", "type": "INT", "widget": {"name": "max_new_tokens"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "type": "STRING", "links": [193]}], "properties": {"cnr_id": "janus-pro", "ver": "4400129e5c33664ae6e927162a39ba4116f44b8b", "Node name for S&R": "JanusImageUnderstanding"}, "widgets_values": ["Describe this image in detail.", 626247378270975, "randomize", 0.1, 0.95, 512]}], "links": [[51, 8, 0, 9, 0, "IMAGE"], [107, 55, 0, 8, 1, "VAE"], [111, 54, 0, 16, 0, "CLIP"], [112, 54, 0, 40, 0, "CLIP"], [162, 69, 0, 70, 0, "MODEL"], [165, 55, 0, 77, 1, "VAE"], [183, 70, 0, 82, 0, "MODEL"], [185, 16, 0, 82, 1, "CONDITIONING"], [186, 40, 0, 82, 2, "CONDITIONING"], [187, 77, 0, 82, 3, "LATENT"], [188, 82, 0, 8, 0, "LATENT"], [189, 84, 0, 16, 1, "STRING"], [190, 84, 1, 40, 1, "STRING"], [191, 76, 0, 86, 2, "IMAGE"], [192, 87, 0, 86, 0, "JANUS_MODEL"], [193, 86, 0, 84, 0, "STRING"], [194, 87, 1, 86, 1, "JANUS_PROCESSOR"], [195, 76, 0, 88, 0, "IMAGE"], [196, 88, 0, 77, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.8390545288824454, "offset": [1056.7691086548148, 338.46218145822553]}, "frontendVersion": "1.18.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}