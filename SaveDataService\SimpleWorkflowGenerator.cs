using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;

namespace SaveDataService
{
    /// <summary>
    /// 简单的ComfyUI工作流代码生成器
    /// </summary>
    public class SimpleWorkflowGenerator
    {
        /// <summary>
        /// 生成工作流类
        /// </summary>
        public static async Task GenerateWorkflowAsync(string jsonFilePath)
        {
            // 读取JSON文件
            var jsonContent = await File.ReadAllTextAsync(jsonFilePath);
            var workflow = JObject.Parse(jsonContent);
            
            // 获取类名（基于文件名）
            var fileName = Path.GetFileNameWithoutExtension(jsonFilePath);
            var className = ToPascalCase(fileName);
            
            // 分析参数
            var parameters = new List<string>();
            var paramComments = new List<string>();
            var paramReplacements = new List<string>();
            
            foreach (var node in workflow)
            {
                var nodeData = node.Value as JObject;
                if (nodeData == null) continue;
                
                var classType = nodeData["class_type"]?.ToString();
                var inputs = nodeData["inputs"] as JObject;
                if (inputs == null) continue;
                
                switch (classType)
                {
                    case "LoadImage":
                        parameters.Add("byte[] imageBytes");
                        paramComments.Add("        /// <param name=\"imageBytes\">输入图片字节数据</param>");
                        paramReplacements.Add($"                var imageName = await comfyUIManage.UploadImageAsync(imageBytes, serverUrl);");
                        paramReplacements.Add($"                workflow[\"{node.Key}\"][\"inputs\"][\"image\"] = imageName;");
                        break;
                        
                    case "CLIPTextEncode":
                        var text = inputs["text"]?.ToString();
                        if (!string.IsNullOrEmpty(text))
                        {
                            parameters.Add($"string text = \"{text}\"");
                        }
                        else
                        {
                            parameters.Add("string text");
                        }
                        paramComments.Add("        /// <param name=\"text\">文本提示词</param>");
                        paramReplacements.Add($"                workflow[\"{node.Key}\"][\"inputs\"][\"text\"] = text;");
                        break;
                        
                    case "EmptyLatentImage":
                        var width = inputs["width"]?.ToObject<int>();
                        var height = inputs["height"]?.ToObject<int>();
                        
                        if (width.HasValue)
                        {
                            parameters.Add($"int width = {width.Value}");
                            paramComments.Add("        /// <param name=\"width\">图片宽度</param>");
                            paramReplacements.Add($"                workflow[\"{node.Key}\"][\"inputs\"][\"width\"] = width;");
                        }
                        
                        if (height.HasValue)
                        {
                            parameters.Add($"int height = {height.Value}");
                            paramComments.Add("        /// <param name=\"height\">图片高度</param>");
                            paramReplacements.Add($"                workflow[\"{node.Key}\"][\"inputs\"][\"height\"] = height;");
                        }
                        break;
                        
                    case "KSampler":
                        var steps = inputs["steps"]?.ToObject<int>();
                        var cfg = inputs["cfg"]?.ToObject<double>();
                        
                        if (steps.HasValue)
                        {
                            parameters.Add($"int steps = {steps.Value}");
                            paramComments.Add("        /// <param name=\"steps\">采样步数</param>");
                            paramReplacements.Add($"                workflow[\"{node.Key}\"][\"inputs\"][\"steps\"] = steps;");
                        }
                        
                        if (cfg.HasValue)
                        {
                            parameters.Add($"double cfg = {cfg.Value:F1}d");
                            paramComments.Add("        /// <param name=\"cfg\">CFG引导强度</param>");
                            paramReplacements.Add($"                workflow[\"{node.Key}\"][\"inputs\"][\"cfg\"] = cfg;");
                        }
                        break;
                }
            }
            
            // 生成类代码
            var sb = new StringBuilder();
            sb.AppendLine("using System;");
            sb.AppendLine("using System.Threading.Tasks;");
            sb.AppendLine("using Newtonsoft.Json.Linq;");
            sb.AppendLine();
            sb.AppendLine("namespace SaveDataService.ComfyuiGate");
            sb.AppendLine("{");
            sb.AppendLine("    /// <summary>");
            sb.AppendLine($"    /// {className} - ComfyUI工作流");
            sb.AppendLine("    /// </summary>");
            sb.AppendLine($"    public class {className}");
            sb.AppendLine("    {");
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 原始工作流JSON");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        private static readonly string OriginalWorkflowJson = @\"");
            sb.AppendLine(jsonContent.Replace("\"", "\"\""));
            sb.AppendLine("        \";");
            sb.AppendLine();
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 执行工作流");
            sb.AppendLine("        /// </summary>");
            
            // 添加参数注释
            foreach (var comment in paramComments)
            {
                sb.AppendLine(comment);
            }
            sb.AppendLine("        /// <param name=\"serverUrl\">ComfyUI服务器地址（可选）</param>");
            sb.AppendLine("        /// <returns>执行结果</returns>");
            
            // 方法签名
            parameters.Add("string serverUrl = null");
            sb.AppendLine($"        public static async Task<string> runWorkflow({string.Join(", ", parameters)})");
            sb.AppendLine("        {");
            sb.AppendLine("            try");
            sb.AppendLine("            {");
            sb.AppendLine("                var workflow = JObject.Parse(OriginalWorkflowJson);");
            sb.AppendLine("                var comfyUIManage = new ComfyUIManage();");
            sb.AppendLine();
            
            // 参数替换
            foreach (var replacement in paramReplacements)
            {
                sb.AppendLine(replacement);
            }
            
            sb.AppendLine();
            sb.AppendLine("                var result = await comfyUIManage.ExecuteWorkflowAsync(workflow.ToString(), serverUrl);");
            sb.AppendLine("                return result;");
            sb.AppendLine("            }");
            sb.AppendLine("            catch (Exception ex)");
            sb.AppendLine("            {");
            sb.AppendLine($"                throw new Exception($\"{className}执行失败: {{ex.Message}}\", ex);");
            sb.AppendLine("            }");
            sb.AppendLine("        }");
            sb.AppendLine("    }");
            sb.AppendLine("}");
            
            // 保存文件
            var outputDir = Path.Combine(Directory.GetCurrentDirectory(), "ComfyuiGate");
            if (!Directory.Exists(outputDir))
            {
                Directory.CreateDirectory(outputDir);
            }
            
            var outputPath = Path.Combine(outputDir, $"{className}.cs");
            await File.WriteAllTextAsync(outputPath, sb.ToString());
            
            Console.WriteLine($"生成工作流类: {outputPath}");
        }

        /// <summary>
        /// 转换为Pascal命名
        /// </summary>
        private static string ToPascalCase(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;
            
            var sb = new StringBuilder();
            bool capitalizeNext = true;
            
            foreach (char c in input)
            {
                if (char.IsLetterOrDigit(c))
                {
                    if (capitalizeNext)
                    {
                        sb.Append(char.ToUpper(c));
                        capitalizeNext = false;
                    }
                    else
                    {
                        sb.Append(c);
                    }
                }
                else
                {
                    capitalizeNext = true;
                }
            }
            
            return sb.ToString();
        }

        /// <summary>
        /// 生成所有工作流
        /// </summary>
        public static async Task GenerateAllWorkflowsAsync()
        {
            var workflowDir = Path.Combine(Directory.GetCurrentDirectory(), "Res", "comfyuiWorkFlows");
            if (!Directory.Exists(workflowDir))
            {
                Console.WriteLine($"工作流目录不存在: {workflowDir}");
                return;
            }

            var jsonFiles = Directory.GetFiles(workflowDir, "*.json", SearchOption.AllDirectories);
            Console.WriteLine($"找到 {jsonFiles.Length} 个工作流文件");

            foreach (var jsonFile in jsonFiles)
            {
                try
                {
                    await GenerateWorkflowAsync(jsonFile);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"生成工作流类失败 {jsonFile}: {ex.Message}");
                }
            }
        }
    }
}
