{"id": "01d66ae9-78be-4a8d-b737-24eee5e1d447", "revision": 0, "last_node_id": 75, "last_link_id": 165, "nodes": [{"id": 55, "type": "VAELoader", "pos": [1010, 290], "size": [310, 60], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [107]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "VAELoader", "widget_ue_connectable": {}}, "widgets_values": ["ae.safetensors"]}, {"id": 54, "type": "QuadrupleCLIPLoader", "pos": [80, 50], "size": [400, 130], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "clip_name1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "clip_name2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "clip_name3", "name": "clip_name3", "type": "COMBO", "widget": {"name": "clip_name3"}, "link": null}, {"localized_name": "clip_name4", "name": "clip_name4", "type": "COMBO", "widget": {"name": "clip_name4"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [111, 112]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "QuadrupleCLIPLoader", "widget_ue_connectable": {}}, "widgets_values": ["clip_l_hidream.safetensors", "clip_g_hidream.safetensors", "t5xxl_fp8_e4m3fn_scaled.safetensors", "llama_3.1_8b_instruct_fp8_scaled.safetensors"], "color": "#223", "bgcolor": "#335"}, {"id": 72, "type": "<PERSON>downNote", "pos": [1010, 430], "size": [300, 450], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "title": "Sampling Settings", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["## Official sampling settings\n\nProvided for reference, my workflows may have slightly different settings.\n\n### HiDream Full\n\n* hidream_i1_full_fp16.safetensors\n* shift: 3.0\n* steps: 50\n* sampler: uni_pc\n* scheduler: simple\n* cfg: 5.0\n\n### HiDream Dev\n\n* hidream_i1_dev_bf16.safetensors\n* shift: 6.0\n* steps: 28\n* sampler: lcm\n* scheduler: normal\n* cfg: 1.0 (no negative prompt)\n\n### HiDream Fast\n\n* hidream_i1_fast_bf16.safetensors\n* shift: 3.0\n* steps: 16\n* sampler: lcm\n* scheduler: normal\n* cfg: 1.0 (no negative prompt)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 70, "type": "ModelSamplingSD3", "pos": [750, -90], "size": [210, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 165}, {"localized_name": "移位", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [163]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "ModelSamplingSD3", "widget_ue_connectable": {}}, "widgets_values": [3.0000000000000004]}, {"id": 53, "type": "EmptySD3LatentImage", "pos": [640, 500], "size": [315, 106], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "批量大小", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [100]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "EmptySD3LatentImage", "widget_ue_connectable": {}}, "widgets_values": [1024, 1024, 1]}, {"id": 73, "type": "Note", "pos": [221.36090087890625, -222.29476928710938], "size": [250, 88], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["You can try changing the weight_dtype to fp8 if you are running out of memory."], "color": "#432", "bgcolor": "#653"}, {"id": 40, "type": "CLIPTextEncode", "pos": [530, 260], "size": [432, 192], "flags": {"collapsed": false}, "order": 7, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 112}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [114]}], "title": "Negative Prompt", "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["blurry", [false, true]], "color": "#322", "bgcolor": "#533"}, {"id": 16, "type": "CLIPTextEncode", "pos": [530, 20], "size": [432, 192], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 111}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [21]}], "title": "Positive Prompt", "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["anime girl with massive fennec ears and a big fluffy fox tail with long wavy blonde hair and blue eyes wearing a pink sweater a large oversized black winter coat and a long blue maxi skirt and large winter boots and a red scarf and large gloves sitting in a sled sledding fast down a snow mountain", [false, true]], "color": "#232", "bgcolor": "#353"}, {"id": 75, "type": "TeaCache", "pos": [714.1507568359375, -301.98236083984375], "size": [270, 130], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 164}, {"localized_name": "model_type", "name": "model_type", "type": "COMBO", "widget": {"name": "model_type"}, "link": null}, {"localized_name": "rel_l1_thresh", "name": "rel_l1_thresh", "type": "FLOAT", "widget": {"name": "rel_l1_thresh"}, "link": null}, {"localized_name": "start_percent", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "end_percent", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}], "outputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "links": [165]}], "properties": {"cnr_id": "teacache", "ver": "efe06530d43486df4431d4c7dea1873b738c647a", "Node name for S&R": "TeaCache", "widget_ue_connectable": {}}, "widgets_values": ["hidream_i1_full", 0.3500000000000001, 0.10000000000000002, 1]}, {"id": 69, "type": "UNETLoader", "pos": [80, -90], "size": [400, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [164]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "UNETLoader", "widget_ue_connectable": {}}, "widgets_values": ["hidream_i1_full_fp8.safetensors", "default"], "color": "#223", "bgcolor": "#335"}, {"id": 8, "type": "VAEDecode", "pos": [1065.0396728515625, 72.25646209716797], "size": [210, 46], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 160}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 107}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [51]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "VAEDecode", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1352.925537109375, -283.5036926269531], "size": [966.615966796875, 1025.3206787109375], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 51}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["teacache"]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [1018.4417724609375, -263.6052551269531], "size": [310, 262], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 163}, {"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 21}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 114}, {"localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 100}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [160]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "K<PERSON><PERSON><PERSON>", "widget_ue_connectable": {}}, "widgets_values": [1023467238681541, "randomize", 30, 5, "euler", "simple", 1]}], "links": [[21, 16, 0, 3, 1, "CONDITIONING"], [51, 8, 0, 9, 0, "IMAGE"], [100, 53, 0, 3, 3, "LATENT"], [107, 55, 0, 8, 1, "VAE"], [111, 54, 0, 16, 0, "CLIP"], [112, 54, 0, 40, 0, "CLIP"], [114, 40, 0, 3, 2, "CONDITIONING"], [160, 3, 0, 8, 0, "LATENT"], [163, 70, 0, 3, 0, "MODEL"], [164, 69, 0, 75, 0, "MODEL"], [165, 75, 0, 70, 0, "MODEL"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.7627768444385521, "offset": [-188.55620976571913, 522.7064688334497]}, "ue_links": [], "frontendVersion": "1.18.10", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true, "links_added_by_ue": []}, "version": 0.4}